import React, { useState, useEffect } from 'react'
import { Box, Container, Paper, Typography, useTheme } from '@mui/material'
import { useTranslation } from 'react-i18next'
import { keyframes } from '@emotion/react'

// Floating animation for background elements
const float = keyframes`
  0% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
  100% { transform: translateY(0px) rotate(0deg); }
`

const floatSlow = keyframes`
  0% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(2deg); }
  100% { transform: translateY(0px) rotate(0deg); }
`

interface AuthLayoutProps {
  children: React.ReactNode
}

export const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  const theme = useTheme()
  const { t } = useTranslation()

  // Slider state
  const [currentSlide, setCurrentSlide] = useState(0)

  // Slider images
  const sliderImages = [
    'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=800&h=600&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=800&h=600&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=800&h=600&fit=crop&crop=center'
  ]

  // Auto slide effect
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % sliderImages.length)
    }, 4000)
    return () => clearInterval(interval)
  }, [sliderImages.length])

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        backgroundColor: theme.palette.background.default,
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Animated Background Elements */}
      <Box
        sx={{
          position: 'absolute',
          top: '10%',
          left: '5%',
          width: 60,
          height: 60,
          borderRadius: '50%',
          backgroundColor: theme.palette.primary.main,
          opacity: 0.1,
          animation: `${float} 6s ease-in-out infinite`,
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          top: '60%',
          right: '10%',
          width: 40,
          height: 40,
          borderRadius: '50%',
          backgroundColor: theme.palette.secondary.main,
          opacity: 0.1,
          animation: `${floatSlow} 8s ease-in-out infinite`,
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          bottom: '20%',
          left: '15%',
          width: 80,
          height: 80,
          borderRadius: '50%',
          backgroundColor: theme.palette.success.main,
          opacity: 0.08,
          animation: `${float} 7s ease-in-out infinite`,
        }}
      />

      <Container maxWidth="lg" sx={{ display: 'flex', alignItems: 'center', py: 4 }}>
        <Paper
          elevation={8}
          sx={{
            display: 'flex',
            width: '100%',
            maxWidth: 1000,
            minHeight: 600,
            mx: 'auto',
            borderRadius: 3,
            overflow: 'hidden',
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          }}
        >
          {/* Left Side - Image Slider */}
          <Box
            sx={{
              flex: 1,
              position: 'relative',
              minHeight: 600,
              overflow: 'hidden',
            }}
          >
            {/* Image Slider */}
            {sliderImages.map((image, index) => (
              <Box
                key={index}
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundImage: `url(${image})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  opacity: currentSlide === index ? 1 : 0,
                  transition: 'opacity 1s ease-in-out',
                }}
              />
            ))}



            {/* Slider Indicators */}
            <Box
              sx={{
                position: 'absolute',
                top: 20,
                right: 20,
                display: 'flex',
                gap: 1,
                zIndex: 2,
              }}
            >
              {sliderImages.map((_, index) => (
                <Box
                  key={index}
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: currentSlide === index ? 'white' : 'rgba(255,255,255,0.5)',
                    transition: 'all 0.3s ease',
                    cursor: 'pointer',
                  }}
                  onClick={() => setCurrentSlide(index)}
                />
              ))}
            </Box>

            {/* Testimonial - Bottom Positioned */}
            <Box
              sx={{
                position: 'absolute',
                bottom: 30,
                left: 30,
                right: 30,
                zIndex: 3,
                p: 3,
                borderRadius: 2,
                backgroundColor: 'rgba(0,0,0,0.7)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255,255,255,0.2)',
                color: 'white',
              }}
            >
              <Typography
                variant="body1"
                sx={{
                  fontStyle: 'italic',
                  mb: 2,
                  lineHeight: 1.6,
                }}
              >
                "{t('auth.hero.testimonial')}"
              </Typography>
              <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                  {t('auth.hero.author')}
                </Typography>
                <Typography variant="caption" sx={{ opacity: 0.8 }}>
                  {t('auth.hero.position')}
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Right Side - Form Section */}
          <Box
            sx={{
              flex: 1,
              p: 6,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              backgroundColor: theme.palette.background.paper,
              minHeight: 600,
            }}
          >
            {children}
          </Box>
        </Paper>
      </Container>
    </Box>
  )
}
