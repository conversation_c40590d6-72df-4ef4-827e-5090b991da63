import React from 'react'
import { Card, CardContent, Typography, Box, Chip } from '@mui/material'
import { useTranslation } from 'react-i18next'
import { 
  WbSunny, 
  Cloud, 
  Grain, 
  AcUnit,
  Thermostat,
  LocationOn 
} from '@mui/icons-material'

interface WeatherData {
  temperature: number
  condition: 'sunny' | 'cloudy' | 'rainy' | 'snowy'
  location: string
}

export const WeatherWidget: React.FC = () => {
  const { t } = useTranslation()
  
  // Mock data - gerçek API entegrasyonu sonra yapılacak
  const weatherData: WeatherData = {
    temperature: 22,
    condition: 'sunny',
    location: 'İstanbul'
  }

  const getWeatherIcon = (condition: string) => {
    switch (condition) {
      case 'sunny':
        return <WbSunny sx={{ fontSize: 40, color: '#FFA726' }} />
      case 'cloudy':
        return <Cloud sx={{ fontSize: 40, color: '#90A4AE' }} />
      case 'rainy':
        return <Grain sx={{ fontSize: 40, color: '#42A5F5' }} />
      case 'snowy':
        return <AcUnit sx={{ fontSize: 40, color: '#E3F2FD' }} />
      default:
        return <WbSunny sx={{ fontSize: 40, color: '#FFA726' }} />
    }
  }

  const getConditionText = (condition: string) => {
    return t(`dashboard.weather.${condition}`)
  }

  const getTemperatureColor = (temp: number) => {
    if (temp < 0) return '#42A5F5' // Mavi (soğuk)
    if (temp < 15) return '#66BB6A' // Yeşil (serin)
    if (temp < 25) return '#FFA726' // Turuncu (ılık)
    return '#F44336' // Kırmızı (sıcak)
  }

  return (
    <Card sx={{
      background: 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)',
      color: 'white',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      '& .MuiCardContent-root': {
        '&:last-child': { pb: 2 },
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between'
      }
    }}>
      <CardContent>
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          height: '100%'
        }}>
          {/* Sol taraf - Lokasyon ve durum */}
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <LocationOn sx={{ mr: 1, fontSize: 32, opacity: 0.9 }} />
            <Box>
              <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                {weatherData.location}
              </Typography>
              <Typography
                variant="caption"
                sx={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  color: 'white',
                  fontWeight: 500,
                  px: 1,
                  py: 0.25,
                  borderRadius: 1,
                  fontSize: '0.75rem',
                  display: 'inline-block'
                }}
              >
                {getConditionText(weatherData.condition)}
              </Typography>
            </Box>
          </Box>

          {/* Sağ taraf - Sıcaklık ve icon */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography
              variant="h3"
              component="div"
              sx={{
                fontWeight: 'bold',
                color: 'white',
                textShadow: '0 2px 4px rgba(0,0,0,0.3)'
              }}
            >
              {weatherData.temperature}°
            </Typography>

            <Box sx={{ transform: 'scale(0.8)' }}>
              {getWeatherIcon(weatherData.condition)}
            </Box>
          </Box>
        </Box>
      </CardContent>
    </Card>
  )
}
