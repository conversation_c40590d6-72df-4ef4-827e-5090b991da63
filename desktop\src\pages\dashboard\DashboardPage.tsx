import React from 'react'
import { Box, Container, Grid, Typography } from '@mui/material'
import { useTranslation } from 'react-i18next'
import { NotificationPanel } from '../../components/dashboard/NotificationPanel'
import { WeatherWidget } from '../../components/dashboard/WeatherWidget'
import { ClockWidget } from '../../components/dashboard/ClockWidget'

export const DashboardPage: React.FC = () => {
  const { t } = useTranslation()

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        {t('dashboard.title')}
      </Typography>
      
      <Grid container spacing={3}>
        {/* Sol Panel - Bildirimler */}
        <Grid item xs={12} md={5} lg={4}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {/* Üst Satır - Saat ve Hava Durumu Yan <PERSON> */}
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Box sx={{ height: '140px' }}>
                  <ClockWidget />
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box sx={{ height: '140px' }}>
                  <WeatherWidget />
                </Box>
              </Grid>
            </Grid>

            {/* Alt Satır - Bildirimler Paneli */}
            <NotificationPanel />
          </Box>
        </Grid>

        {/* Sağ Panel - Ana İçerik */}
        <Grid item xs={12} md={7} lg={8}>
          <Box sx={{ 
            minHeight: '600px',
            backgroundColor: 'background.paper',
            borderRadius: 2,
            p: 3,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Typography variant="h6" color="text.secondary">
              Ana dashboard içeriği burada geliştirilecek...
            </Typography>
          </Box>
        </Grid>
      </Grid>
    </Container>
  )
}
