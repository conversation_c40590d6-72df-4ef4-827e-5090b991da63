import React from 'react'
import { Box, Container, Typography, Grid } from '@mui/material'
import { useTranslation } from 'react-i18next'
import { NotificationPanel, WeatherWidget, ClockWidget } from '../../components/dashboard'

export const DashboardPage: React.FC = () => {
  const { t } = useTranslation()

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        {t('dashboard.title')}
      </Typography>
      
      <Grid container spacing={3}>
        {/* Sol Panel - Bildirimler - ÇOK GENİŞ! */}
        <Grid item xs={12} md={8} lg={7}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {/* Saat Widget - Full Width */}
            <Box sx={{ height: '120px' }}>
              <ClockWidget />
            </Box>

            {/* Hava Durumu Widget - Full Width */}
            <Box sx={{ height: '100px' }}>
              <WeatherWidget />
            </Box>

            {/* Bildirimler Paneli - Full Width */}
            <NotificationPanel />
          </Box>
        </Grid>

        {/* Sağ Panel - Ana İçerik - Küçük */}
        <Grid item xs={12} md={4} lg={5}>
          <Box sx={{ 
            minHeight: '600px',
            backgroundColor: 'background.paper',
            borderRadius: 2,
            p: 3,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Typography variant="h6" color="text.secondary">
              Ana dashboard içeriği burada geliştirilecek...
            </Typography>
          </Box>
        </Grid>
      </Grid>
    </Container>
  )
}
