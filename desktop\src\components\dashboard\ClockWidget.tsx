import React, { useState, useEffect } from 'react'
import { Card, CardContent, Typography, Box } from '@mui/material'
import { useTranslation } from 'react-i18next'
import { AccessTime, CalendarToday } from '@mui/icons-material'

export const ClockWidget: React.FC = () => {
  const { t } = useTranslation()
  const [currentTime, setCurrentTime] = useState(new Date())

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('tr-TR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <Card sx={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      '& .MuiCardContent-root': {
        '&:last-child': { pb: 2 },
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between'
      }
    }}>
      <CardContent sx={{ py: 2, textAlign: 'center' }}>
        <Box sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
          gap: 1
        }}>


          {/* Orta - Tarih */}
          <Typography
            variant="body2"
            sx={{
              fontWeight: 'bold',
              opacity: 0.9,
              mb: 0.5
            }}
          >
            {formatDate(currentTime)}
          </Typography>

          {/* Alt - Büyük Saat */}
          <Typography
            variant="h3"
            component="div"
            sx={{
              fontWeight: 'bold',
              fontFamily: 'monospace',
              textShadow: '0 2px 4px rgba(0,0,0,0.2)',
              lineHeight: 1
            }}
          >
            {formatTime(currentTime)}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  )
}
