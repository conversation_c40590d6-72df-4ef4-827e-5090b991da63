import React, { useState, useEffect } from 'react'
import { Card, CardContent, Typography, Box } from '@mui/material'
import { useTranslation } from 'react-i18next'
import { AccessTime, CalendarToday } from '@mui/icons-material'

export const ClockWidget: React.FC = () => {
  const { t } = useTranslation()
  const [currentTime, setCurrentTime] = useState(new Date())

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('tr-TR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <Card sx={{ 
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white',
      '& .MuiCardContent-root': {
        '&:last-child': { pb: 2 }
      }
    }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <AccessTime sx={{ mr: 1, fontSize: 20 }} />
          <Typography variant="subtitle2" sx={{ opacity: 0.9 }}>
            {t('dashboard.clock.currentTime')}
          </Typography>
        </Box>
        
        <Typography
          variant="h5"
          component="div"
          sx={{
            fontWeight: 'bold',
            fontFamily: 'monospace',
            mb: 1,
            textAlign: 'center'
          }}
        >
          {formatTime(currentTime)}
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <CalendarToday sx={{ mr: 0.5, fontSize: 12 }} />
          <Typography variant="caption" sx={{ opacity: 0.9, textAlign: 'center', fontSize: '0.7rem' }}>
            {formatDate(currentTime).split(' ').slice(0, 2).join(' ')}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  )
}
